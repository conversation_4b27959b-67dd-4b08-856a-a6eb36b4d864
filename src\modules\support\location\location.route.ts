import { Router } from 'express';
import { controllerWrapper } from '@/utils/controller-wrapper';
import * as locationController from './location.controller';

const router = Router();

/**
 * @route GET /api/locations/countries
 * @desc Get all countries with optional filtering and pagination
 * @query search - Search by country name, ISO code, or ISO3 code
 * @query region - Filter by region (Asia, Europe, etc.)
 * @query limit - Number of results per page (default: 50)
 * @query offset - Number of results to skip (default: 0)
 */
router.get('/countries', controllerWrapper(
  locationController.getCountries,
  { 
    needsTransaction: false,
    successMessage: 'Countries retrieved successfully'
  }
));

/**
 * @route GET /api/locations/countries/:id
 * @desc Get country by ID with detailed information
 * @param id - Country ID
 */
router.get('/countries/:id', controllerWrapper(
  locationController.getCountryById,
  { 
    needsTransaction: false,
    successMessage: 'Country details retrieved successfully'
  }
));

/**
 * @route GET /api/locations/countries/:id/states
 * @desc Get states by country ID
 * @param id - Country ID
 * @query search - Search by state name
 * @query limit - Number of results per page (default: 50)
 * @query offset - Number of results to skip (default: 0)
 */
router.get('/countries/:id/states', controllerWrapper(
  locationController.getStatesByCountry,
  { 
    needsTransaction: false,
    successMessage: 'States retrieved successfully'
  }
));

/**
 * @route GET /api/locations/states/:id/cities
 * @desc Get cities by state ID
 * @param id - State ID
 * @query search - Search by city name
 * @query limit - Number of results per page (default: 100)
 * @query offset - Number of results to skip (default: 0)
 */
router.get('/states/:id/cities', controllerWrapper(
  locationController.getCitiesByState,
  { 
    needsTransaction: false,
    successMessage: 'Cities retrieved successfully'
  }
));

/**
 * @route GET /api/locations/search
 * @desc Search across countries, states, and cities
 * @query q - Search query (required)
 * @query types - Comma-separated list of types to search (country,state,city)
 * @query limit - Maximum number of results (default: 20)
 */
router.get('/search', controllerWrapper(
  locationController.searchLocations,
  { 
    needsTransaction: false,
    successMessage: 'Location search completed successfully'
  }
));

/**
 * @route GET /api/locations/cities/:id/hierarchy
 * @desc Get complete location hierarchy for a city (city -> state -> country)
 * @param id - City ID
 */
router.get('/cities/:id/hierarchy', controllerWrapper(
  locationController.getLocationHierarchy,
  { 
    needsTransaction: false,
    successMessage: 'Location hierarchy retrieved successfully'
  }
));

/**
 * @route GET /api/locations/regions
 * @desc Get all unique regions from countries
 */
router.get('/regions', controllerWrapper(
  locationController.getRegions,
  { 
    needsTransaction: false,
    successMessage: 'Regions retrieved successfully'
  }
));

export default router;
