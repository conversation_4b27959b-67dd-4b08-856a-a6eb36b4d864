
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('@prisma/client/runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.9.0
 * Query Engine version: 81e4af48011447c3cc503a190e86995b66d2a28e
 */
Prisma.prismaVersion = {
  client: "6.9.0",
  engine: "81e4af48011447c3cc503a190e86995b66d2a28e"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.UserScalarFieldEnum = {
  userId: 'userId',
  email: 'email',
  firstName: 'firstName',
  lastName: 'lastName',
  profileImage: 'profileImage',
  type: 'type',
  phoneNumber: 'phoneNumber',
  countryDialCode: 'countryDialCode',
  candidateId: 'candidateId',
  recruiterId: 'recruiterId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  createdBy: 'createdBy',
  updatedBy: 'updatedBy',
  status: 'status'
};

exports.Prisma.CandidateScalarFieldEnum = {
  candidateId: 'candidateId',
  resume: 'resume',
  currentLocationId: 'currentLocationId',
  summary: 'summary',
  totalExpirenceMonths: 'totalExpirenceMonths',
  totalExpirenceWithoutInternMonths: 'totalExpirenceWithoutInternMonths',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  createdBy: 'createdBy',
  updatedBy: 'updatedBy'
};

exports.Prisma.LocationScalarFieldEnum = {
  locationId: 'locationId',
  addressLine1: 'addressLine1',
  addressLine2: 'addressLine2',
  cityId: 'cityId',
  stateId: 'stateId',
  countryId: 'countryId',
  zipCode: 'zipCode',
  latitude: 'latitude',
  longitude: 'longitude',
  createdAt: 'createdAt',
  createdBy: 'createdBy',
  updatedAt: 'updatedAt',
  updatedBy: 'updatedBy'
};

exports.Prisma.OrganizationLocationScalarFieldEnum = {
  organizationLocationId: 'organizationLocationId',
  locationId: 'locationId',
  organizationId: 'organizationId',
  isDefault: 'isDefault',
  createdAt: 'createdAt',
  createdBy: 'createdBy',
  updatedAt: 'updatedAt',
  updatedBy: 'updatedBy'
};

exports.Prisma.OrganizationScalarFieldEnum = {
  organizationId: 'organizationId',
  name: 'name',
  domain: 'domain',
  email: 'email',
  phoneNumber: 'phoneNumber',
  logo: 'logo',
  organizationOverview: 'organizationOverview',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  createdBy: 'createdBy',
  updatedBy: 'updatedBy',
  status: 'status'
};

exports.Prisma.RecruiterScalarFieldEnum = {
  recruiterId: 'recruiterId',
  organizationId: 'organizationId',
  roleId: 'roleId',
  status: 'status',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  createdBy: 'createdBy',
  updatedBy: 'updatedBy'
};

exports.Prisma.RoleScalarFieldEnum = {
  roleId: 'roleId',
  name: 'name',
  organizationId: 'organizationId',
  global: 'global',
  createdAt: 'createdAt',
  createdBy: 'createdBy',
  updatedAt: 'updatedAt',
  updatedBy: 'updatedBy',
  status: 'status'
};

exports.Prisma.PermissionScalarFieldEnum = {
  permissionId: 'permissionId',
  description: 'description',
  module: 'module',
  code: 'code',
  createdAt: 'createdAt',
  createdBy: 'createdBy',
  updatedAt: 'updatedAt',
  updatedBy: 'updatedBy',
  status: 'status'
};

exports.Prisma.RolePermissionScalarFieldEnum = {
  rolePermissionId: 'rolePermissionId',
  roleId: 'roleId',
  permissionId: 'permissionId',
  createdAt: 'createdAt',
  createdBy: 'createdBy',
  updatedAt: 'updatedAt',
  updatedBy: 'updatedBy'
};

exports.Prisma.UserSessionScalarFieldEnum = {
  userSessionsId: 'userSessionsId',
  userId: 'userId',
  ipAddress: 'ipAddress',
  userAgent: 'userAgent',
  loginMethod: 'loginMethod',
  issuedAt: 'issuedAt',
  expiresAt: 'expiresAt',
  status: 'status'
};

exports.Prisma.CityScalarFieldEnum = {
  cityId: 'cityId',
  cityName: 'cityName',
  stateId: 'stateId',
  createdAt: 'createdAt',
  createdBy: 'createdBy',
  updatedAt: 'updatedAt',
  updatedBy: 'updatedBy'
};

exports.Prisma.StateScalarFieldEnum = {
  stateId: 'stateId',
  stateName: 'stateName',
  countryId: 'countryId',
  createdAt: 'createdAt',
  createdBy: 'createdBy',
  updatedAt: 'updatedAt',
  updatedBy: 'updatedBy'
};

exports.Prisma.CountryScalarFieldEnum = {
  countryId: 'countryId',
  countryName: 'countryName',
  countryCode: 'countryCode',
  createdAt: 'createdAt',
  phoneNumberLength: 'phoneNumberLength',
  countryDialCode: 'countryDialCode',
  createdBy: 'createdBy',
  updatedAt: 'updatedAt',
  updatedBy: 'updatedBy'
};

exports.Prisma.OtpScalarFieldEnum = {
  otpId: 'otpId',
  otp: 'otp',
  tokenId: 'tokenId',
  expiredAt: 'expiredAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};

exports.Prisma.UserOrderByRelevanceFieldEnum = {
  email: 'email',
  firstName: 'firstName',
  lastName: 'lastName',
  profileImage: 'profileImage',
  phoneNumber: 'phoneNumber',
  countryDialCode: 'countryDialCode',
  status: 'status'
};

exports.Prisma.CandidateOrderByRelevanceFieldEnum = {
  summary: 'summary'
};

exports.Prisma.LocationOrderByRelevanceFieldEnum = {
  addressLine1: 'addressLine1',
  addressLine2: 'addressLine2',
  zipCode: 'zipCode'
};

exports.Prisma.OrganizationOrderByRelevanceFieldEnum = {
  name: 'name',
  domain: 'domain',
  email: 'email',
  phoneNumber: 'phoneNumber',
  logo: 'logo',
  organizationOverview: 'organizationOverview',
  status: 'status'
};

exports.Prisma.RecruiterOrderByRelevanceFieldEnum = {
  status: 'status'
};

exports.Prisma.RoleOrderByRelevanceFieldEnum = {
  name: 'name'
};

exports.Prisma.PermissionOrderByRelevanceFieldEnum = {
  description: 'description',
  module: 'module',
  code: 'code',
  status: 'status'
};

exports.Prisma.UserSessionOrderByRelevanceFieldEnum = {
  ipAddress: 'ipAddress',
  userAgent: 'userAgent',
  status: 'status'
};

exports.Prisma.CityOrderByRelevanceFieldEnum = {
  cityName: 'cityName'
};

exports.Prisma.StateOrderByRelevanceFieldEnum = {
  stateName: 'stateName'
};

exports.Prisma.CountryOrderByRelevanceFieldEnum = {
  countryName: 'countryName',
  countryCode: 'countryCode',
  countryDialCode: 'countryDialCode'
};
exports.UserType = exports.$Enums.UserType = {
  candidate: 'candidate',
  recruiter: 'recruiter',
  system: 'system'
};

exports.LoginMethod = exports.$Enums.LoginMethod = {
  google: 'google',
  microsoft: 'microsoft',
  linkedin: 'linkedin',
  otp: 'otp'
};

exports.Prisma.ModelName = {
  User: 'User',
  Candidate: 'Candidate',
  Location: 'Location',
  OrganizationLocation: 'OrganizationLocation',
  Organization: 'Organization',
  Recruiter: 'Recruiter',
  Role: 'Role',
  Permission: 'Permission',
  RolePermission: 'RolePermission',
  UserSession: 'UserSession',
  City: 'City',
  State: 'State',
  Country: 'Country',
  Otp: 'Otp'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
