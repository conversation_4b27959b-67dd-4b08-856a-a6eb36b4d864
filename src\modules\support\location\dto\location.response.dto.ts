import { Timezone, CountryTranslations } from '@/types/location.types';

// Base response interface
interface BaseResponse {
  success: boolean;
  message: string;
}

// Pagination interface
export interface PaginationDto {
  total: number;
  limit: number;
  offset: number;
  hasMore: boolean;
}

// Country response DTOs
export interface CountryDto {
  countryId: number;
  countryName: string;
  isoCode: string;
  iso3Code: string;
  numericCode?: string;
  phoneCode: string;
  capital?: string;
  currency?: string;
  currencyName?: string;
  currencySymbol?: string;
  tld?: string;
  native?: string;
  region?: string;
  subregion?: string;
  latitude?: number;
  longitude?: number;
  emoji?: string;
  emojiU?: string;
  timezones?: Timezone[];
  translations?: CountryTranslations;
  createdAt: Date;
  updatedAt: Date;
}

export interface CountriesResponseDto extends BaseResponse {
  data: CountryDto[];
  pagination: PaginationDto;
}

export interface CountryDetailResponseDto extends BaseResponse {
  data: CountryDto & {
    states?: StateDto[];
  };
}

// State response DTOs
export interface StateDto {
  stateId: number;
  stateName: string;
  stateCode?: string;
  countryId: number;
  type?: string;
  latitude?: number;
  longitude?: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface StatesResponseDto extends BaseResponse {
  data: StateDto[];
  country: {
    countryId: number;
    countryName: string;
    isoCode: string;
  };
  pagination: PaginationDto;
}

// City response DTOs
export interface CityDto {
  cityId: number;
  cityName: string;
  stateId: number;
  latitude?: number;
  longitude?: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface CitiesResponseDto extends BaseResponse {
  data: CityDto[];
  state: {
    stateId: number;
    stateName: string;
    stateCode?: string;
  };
  country: {
    countryId: number;
    countryName: string;
    isoCode: string;
  };
  pagination: PaginationDto;
}

// Search response DTOs
export interface LocationSearchItemDto {
  type: 'country' | 'state' | 'city';
  id: number;
  name: string;
  hierarchy: {
    country: CountryDto;
    state?: StateDto;
    city?: CityDto;
  };
}

export interface LocationSearchResponseDto extends BaseResponse {
  data: LocationSearchItemDto[];
}

// Hierarchy response DTO
export interface LocationHierarchyDto {
  country: CountryDto;
  state: StateDto;
  city: CityDto;
}

export interface LocationHierarchyResponseDto extends BaseResponse {
  data: LocationHierarchyDto;
}

// Regions response DTO
export interface RegionsResponseDto extends BaseResponse {
  data: string[];
}

// Request DTOs for validation
export interface CountryQueryDto {
  search?: string;
  region?: string;
  limit?: number;
  offset?: number;
}

export interface StateQueryDto {
  search?: string;
  limit?: number;
  offset?: number;
}

export interface CityQueryDto {
  search?: string;
  limit?: number;
  offset?: number;
}

export interface SearchQueryDto {
  q: string;
  types?: ('country' | 'state' | 'city')[];
  limit?: number;
}
