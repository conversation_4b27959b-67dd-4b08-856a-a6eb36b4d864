import { Request } from 'express';
import * as locationService from './location.service';
import { z } from 'zod';

// Validation schemas
const paginationSchema = z.object({
  limit: z.string().optional().transform(val => val ? parseInt(val) : undefined),
  offset: z.string().optional().transform(val => val ? parseInt(val) : undefined),
  search: z.string().optional()
});

const countryQuerySchema = paginationSchema.extend({
  region: z.string().optional()
});

const searchSchema = z.object({
  q: z.string().min(1, 'Search query is required'),
  types: z.string().optional().transform(val => 
    val ? val.split(',').filter(t => ['country', 'state', 'city'].includes(t)) : undefined
  ),
  limit: z.string().optional().transform(val => val ? parseInt(val) : undefined)
});

const idParamSchema = z.object({
  id: z.string().transform(val => parseInt(val))
});

/**
 * Get all countries
 * GET /api/locations/countries
 */
export const getCountries = async (req: Request) => {
  const query = countryQuerySchema.parse(req.query);
  
  const result = await locationService.getAllCountries({
    search: query.search,
    region: query.region,
    limit: query.limit,
    offset: query.offset
  });

  return {
    success: true,
    data: result.countries,
    pagination: result.pagination,
    message: `Retrieved ${result.countries.length} countries`
  };
};

/**
 * Get country by ID
 * GET /api/locations/countries/:id
 */
export const getCountryById = async (req: Request) => {
  const { id } = idParamSchema.parse(req.params);
  
  const country = await locationService.getCountryById(id);

  return {
    success: true,
    data: country,
    message: 'Country retrieved successfully'
  };
};

/**
 * Get states by country ID
 * GET /api/locations/countries/:id/states
 */
export const getStatesByCountry = async (req: Request) => {
  const { id } = idParamSchema.parse(req.params);
  const query = paginationSchema.parse(req.query);
  
  const result = await locationService.getStatesByCountryId(id, {
    search: query.search,
    limit: query.limit,
    offset: query.offset
  });

  return {
    success: true,
    data: result.states,
    country: result.country,
    pagination: result.pagination,
    message: `Retrieved ${result.states.length} states`
  };
};

/**
 * Get cities by state ID
 * GET /api/locations/states/:id/cities
 */
export const getCitiesByState = async (req: Request) => {
  const { id } = idParamSchema.parse(req.params);
  const query = paginationSchema.parse(req.query);
  
  const result = await locationService.getCitiesByStateId(id, {
    search: query.search,
    limit: query.limit,
    offset: query.offset
  });

  return {
    success: true,
    data: result.cities,
    state: result.state,
    country: result.country,
    pagination: result.pagination,
    message: `Retrieved ${result.cities.length} cities`
  };
};

/**
 * Search locations
 * GET /api/locations/search
 */
export const searchLocations = async (req: Request) => {
  const query = searchSchema.parse(req.query);
  
  const results = await locationService.searchLocations(query.q, {
    types: query.types as ('country' | 'state' | 'city')[],
    limit: query.limit
  });

  return {
    success: true,
    data: results,
    message: `Found ${results.length} locations matching "${query.q}"`
  };
};

/**
 * Get location hierarchy by city ID
 * GET /api/locations/cities/:id/hierarchy
 */
export const getLocationHierarchy = async (req: Request) => {
  const { id } = idParamSchema.parse(req.params);
  
  const hierarchy = await locationService.getLocationHierarchy(id);

  return {
    success: true,
    data: hierarchy,
    message: 'Location hierarchy retrieved successfully'
  };
};

/**
 * Get all regions
 * GET /api/locations/regions
 */
export const getRegions = async (req: Request) => {
  const regions = await locationService.getRegions();

  return {
    success: true,
    data: regions,
    message: `Retrieved ${regions.length} regions`
  };
};
