import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// Sample comprehensive country data with timezones
const sampleCountries = [
  {
    countryName: 'India',
    isoCode: 'IN',
    iso3Code: 'IND',
    numericCode: '356',
    phoneCode: '+91',
    capital: 'New Delhi',
    currency: 'INR',
    currencyName: 'Indian Rupee',
    currencySymbol: '₹',
    tld: '.in',
    native: 'भारत',
    region: 'Asia',
    subregion: 'Southern Asia',
    latitude: 20.00000000,
    longitude: 77.00000000,
    emoji: '🇮🇳',
    emojiU: 'U+1F1EE U+1F1F3',
    timezones: [
      {
        zoneName: 'Asia/Kolkata',
        gmtOffset: 19800,
        gmtOffsetName: 'UTC+05:30',
        abbreviation: 'IST',
        tzName: 'Indian Standard Time'
      }
    ],
    translations: {
      hi: 'भारत',
      fr: 'Inde',
      es: 'India',
      de: 'Indien',
      ja: 'インド',
      zh: '印度'
    }
  },
  {
    countryName: 'United States',
    isoCode: 'US',
    iso3Code: 'USA',
    numericCode: '840',
    phoneCode: '+1',
    capital: 'Washington, D.C.',
    currency: 'USD',
    currencyName: 'US Dollar',
    currencySymbol: '$',
    tld: '.us',
    native: 'United States',
    region: 'Americas',
    subregion: 'Northern America',
    latitude: 37.09024000,
    longitude: -95.71289100,
    emoji: '🇺🇸',
    emojiU: 'U+1F1FA U+1F1F8',
    timezones: [
      {
        zoneName: 'America/New_York',
        gmtOffset: -18000,
        gmtOffsetName: 'UTC-05:00',
        abbreviation: 'EST',
        tzName: 'Eastern Standard Time'
      },
      {
        zoneName: 'America/Chicago',
        gmtOffset: -21600,
        gmtOffsetName: 'UTC-06:00',
        abbreviation: 'CST',
        tzName: 'Central Standard Time'
      },
      {
        zoneName: 'America/Denver',
        gmtOffset: -25200,
        gmtOffsetName: 'UTC-07:00',
        abbreviation: 'MST',
        tzName: 'Mountain Standard Time'
      },
      {
        zoneName: 'America/Los_Angeles',
        gmtOffset: -28800,
        gmtOffsetName: 'UTC-08:00',
        abbreviation: 'PST',
        tzName: 'Pacific Standard Time'
      }
    ],
    translations: {
      fr: 'États-Unis',
      es: 'Estados Unidos',
      de: 'Vereinigte Staaten',
      ja: 'アメリカ合衆国',
      zh: '美国'
    }
  },
  {
    countryName: 'United Kingdom',
    isoCode: 'GB',
    iso3Code: 'GBR',
    numericCode: '826',
    phoneCode: '+44',
    capital: 'London',
    currency: 'GBP',
    currencyName: 'British Pound',
    currencySymbol: '£',
    tld: '.uk',
    native: 'United Kingdom',
    region: 'Europe',
    subregion: 'Northern Europe',
    latitude: 55.37805100,
    longitude: -3.43597200,
    emoji: '🇬🇧',
    emojiU: 'U+1F1EC U+1F1E7',
    timezones: [
      {
        zoneName: 'Europe/London',
        gmtOffset: 0,
        gmtOffsetName: 'UTC±00',
        abbreviation: 'GMT',
        tzName: 'Greenwich Mean Time'
      }
    ],
    translations: {
      fr: 'Royaume-Uni',
      es: 'Reino Unido',
      de: 'Vereinigtes Königreich',
      ja: 'イギリス',
      zh: '英国'
    }
  }
];

// Sample states data
const sampleStates = [
  // India states
  {
    stateName: 'Maharashtra',
    stateCode: 'MH',
    countryIsoCode: 'IN',
    type: 'State',
    latitude: 19.75147980,
    longitude: 75.71388840
  },
  {
    stateName: 'Karnataka',
    stateCode: 'KA',
    countryIsoCode: 'IN',
    type: 'State',
    latitude: 15.31730480,
    longitude: 75.71388840
  },
  {
    stateName: 'Delhi',
    stateCode: 'DL',
    countryIsoCode: 'IN',
    type: 'Union Territory',
    latitude: 28.70405920,
    longitude: 77.10249020
  },
  // US states
  {
    stateName: 'California',
    stateCode: 'CA',
    countryIsoCode: 'US',
    type: 'State',
    latitude: 36.11626840,
    longitude: -119.68181150
  },
  {
    stateName: 'New York',
    stateCode: 'NY',
    countryIsoCode: 'US',
    type: 'State',
    latitude: 42.16589740,
    longitude: -74.94802620
  },
  // UK regions
  {
    stateName: 'England',
    stateCode: 'ENG',
    countryIsoCode: 'GB',
    type: 'Country',
    latitude: 52.35507450,
    longitude: -1.17432210
  }
];

// Sample cities data
const sampleCities = [
  // Maharashtra cities
  { cityName: 'Mumbai', stateCode: 'MH', latitude: 19.07283000, longitude: 72.88261000 },
  { cityName: 'Pune', stateCode: 'MH', latitude: 18.52043000, longitude: 73.85674000 },
  // Karnataka cities
  { cityName: 'Bangalore', stateCode: 'KA', latitude: 12.97194000, longitude: 77.59369000 },
  { cityName: 'Mysore', stateCode: 'KA', latitude: 12.29791000, longitude: 76.63925000 },
  // Delhi cities
  { cityName: 'New Delhi', stateCode: 'DL', latitude: 28.61394000, longitude: 77.20902000 },
  // California cities
  { cityName: 'Los Angeles', stateCode: 'CA', latitude: 34.05223000, longitude: -118.24368000 },
  { cityName: 'San Francisco', stateCode: 'CA', latitude: 37.77493000, longitude: -122.41942000 },
  // New York cities
  { cityName: 'New York City', stateCode: 'NY', latitude: 40.71427000, longitude: -74.00597000 },
  { cityName: 'Buffalo', stateCode: 'NY', latitude: 42.88645000, longitude: -78.87837000 },
  // England cities
  { cityName: 'London', stateCode: 'ENG', latitude: 51.50853000, longitude: -0.12574000 },
  { cityName: 'Manchester', stateCode: 'ENG', latitude: 53.48071000, longitude: -2.23743000 }
];

async function seedLocationData() {
  console.log('🌍 Starting location data seeding...');

  try {
    // Clear existing data (optional - remove if you want to keep existing data)
    console.log('🗑️  Clearing existing location data...');
    await prisma.city.deleteMany();
    await prisma.state.deleteMany();
    await prisma.country.deleteMany();

    // Seed countries
    console.log('🏳️  Seeding countries...');
    const createdCountries = await Promise.all(
      sampleCountries.map(country =>
        prisma.country.create({
          data: {
            ...country,
            timezones: country.timezones,
            translations: country.translations
          }
        })
      )
    );
    console.log(`✅ Created ${createdCountries.length} countries`);

    // Create country lookup map
    const countryMap = new Map();
    createdCountries.forEach(country => {
      countryMap.set(country.isoCode, country.countryId);
    });

    // Seed states
    console.log('🏛️  Seeding states...');
    const createdStates = await Promise.all(
      sampleStates.map(state =>
        prisma.state.create({
          data: {
            stateName: state.stateName,
            stateCode: state.stateCode,
            countryId: countryMap.get(state.countryIsoCode),
            type: state.type,
            latitude: state.latitude,
            longitude: state.longitude
          }
        })
      )
    );
    console.log(`✅ Created ${createdStates.length} states`);

    // Create state lookup map
    const stateMap = new Map();
    createdStates.forEach(state => {
      stateMap.set(state.stateCode, state.stateId);
    });

    // Seed cities
    console.log('🏙️  Seeding cities...');
    const createdCities = await Promise.all(
      sampleCities.map(city =>
        prisma.city.create({
          data: {
            cityName: city.cityName,
            stateId: stateMap.get(city.stateCode),
            latitude: city.latitude,
            longitude: city.longitude
          }
        })
      )
    );
    console.log(`✅ Created ${createdCities.length} cities`);

    console.log('🎉 Location data seeding completed successfully!');
    console.log(`📊 Summary:
    - Countries: ${createdCountries.length}
    - States: ${createdStates.length}
    - Cities: ${createdCities.length}`);

  } catch (error) {
    console.error('❌ Error seeding location data:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the seeding function
if (require.main === module) {
  seedLocationData()
    .catch((error) => {
      console.error('❌ Seeding failed:', error);
      process.exit(1);
    });
}

export default seedLocationData;
