// Timezone interface for country timezones JSON field
export interface Timezone {
  zoneName: string;           // e.g., "Asia/Kolkata"
  gmtOffset: number;          // GMT offset in seconds, e.g., 19800 for UTC+05:30
  gmtOffsetName: string;      // e.g., "UTC+05:30"
  abbreviation: string;       // e.g., "IST"
  tzName: string;            // e.g., "Indian Standard Time"
}

// Translation interface for country name translations
export interface CountryTranslations {
  [languageCode: string]: string; // e.g., { "hi": "भारत", "fr": "Inde", "es": "India" }
}

// Complete country data interface
export interface CountryData {
  countryId: number;
  countryName: string;
  isoCode: string;           // ISO 3166-1 alpha-2 (e.g., "IN")
  iso3Code: string;          // ISO 3166-1 alpha-3 (e.g., "IND")
  numericCode?: string;      // ISO 3166-1 numeric (e.g., "356")
  phoneCode: string;         // Phone code (e.g., "+91")
  capital?: string;          // Capital city
  currency?: string;         // Currency code (e.g., "INR")
  currencyName?: string;     // Currency name (e.g., "Indian Rupee")
  currencySymbol?: string;   // Currency symbol (e.g., "₹")
  tld?: string;             // Top level domain (e.g., ".in")
  native?: string;          // Native country name
  region?: string;          // Region (e.g., "Asia")
  subregion?: string;       // Subregion (e.g., "Southern Asia")
  latitude?: number;        // Latitude coordinate
  longitude?: number;       // Longitude coordinate
  emoji?: string;           // Country flag emoji
  emojiU?: string;          // Unicode for emoji
  timezones?: Timezone[];   // Array of timezone objects
  translations?: CountryTranslations; // Translation object
}

// State data interface
export interface StateData {
  stateId: number;
  stateName: string;
  stateCode?: string;       // State/Province code
  countryId: number;
  type?: string;           // State, Province, Territory, etc.
  latitude?: number;       // Latitude coordinate
  longitude?: number;      // Longitude coordinate
}

// City data interface
export interface CityData {
  cityId: number;
  cityName: string;
  stateId: number;
  latitude?: number;       // Latitude coordinate
  longitude?: number;      // Longitude coordinate
}

// Location hierarchy interface for API responses
export interface LocationHierarchy {
  country: CountryData;
  state?: StateData;
  city?: CityData;
}

// Search result interface
export interface LocationSearchResult {
  type: 'country' | 'state' | 'city';
  id: number;
  name: string;
  hierarchy: LocationHierarchy;
}
